<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线教育微信小程序原型 - 核心页面展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'blue-25': '#F8FAFF',
                        'blue-50': '#F0F6FF',
                        'blue-75': '#E8F2FF',
                        'blue-100': '#E0EDFF',
                        'blue-200': '#C7DBFF',
                        'blue-300': '#A4C5FF',
                        'blue-400': '#6BA3FF',
                        'blue-500': '#4285FF',
                        'indigo-25': '#F8F9FF',
                        'indigo-50': '#F1F3FF',
                        'indigo-75': '#E9ECFF',
                        'indigo-100': '#E0E5FF',
                        'indigo-200': '#C9D1FF',
                        'indigo-300': '#ADBEFF',
                        'indigo-400': '#8FA7FF',
                        'indigo-500': '#7B92FF',
                        'warm-gray': '#FAFAFA',
                        'text-primary': '#1A1D23',
                        'text-secondary': '#4A5568',
                        'text-tertiary': '#718096',
                        'text-light': '#A0AEC0',
                        'border-light': '#F7FAFC',
                        'border-blue': '#C7DBFF',
                    },
                    fontFamily: {
                        'system': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
                    },
                    boxShadow: {
                        'subtle': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'soft': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; }
        .screen { width: 375px; height: 667px; position: relative; overflow: hidden; }
    </style>
</head>
<body class="bg-warm-gray p-6">
    <div class="max-w-7xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-text-primary mb-2">在线教育微信小程序原型</h1>
            <p class="text-text-secondary">专业级核心页面展示 - 企业级设计标准</p>
        </div>

        <!-- 核心页面展示 -->
        <div class="grid grid-cols-3 gap-6 mb-8">
            
            <!-- 首页 - 学生端 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">首页 - 学生端</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 头部区域 -->
                    <div class="px-6 py-4">
                        <div class="bg-white rounded-lg p-4 shadow-subtle">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face" 
                                         class="w-12 h-12 rounded-full object-cover border-2 border-blue-200">
                                    <div>
                                        <h3 class="font-semibold text-text-primary">李同学，早上好</h3>
                                        <p class="text-xs text-text-secondary">今天也要努力学习哦</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="w-8 h-8 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                                            <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                                        </svg>
                                    </button>
                                    <button class="w-8 h-8 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <circle cx="11" cy="11" r="8"/>
                                            <path d="M21 21l-4.35-4.35"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 学习进度卡片 -->
                    <div class="px-6 mb-4">
                        <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg p-5 text-white relative overflow-hidden">
                            <div class="relative z-10">
                                <h4 class="font-bold mb-2">本周学习进度</h4>
                                <div class="flex items-center space-x-4 mb-3">
                                    <div class="text-center">
                                        <div class="text-xl font-bold">12</div>
                                        <div class="text-xs opacity-90">课时</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xl font-bold">3</div>
                                        <div class="text-xs opacity-90">作业</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xl font-bold">85%</div>
                                        <div class="text-xs opacity-90">完成度</div>
                                    </div>
                                </div>
                                <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                                    <div class="bg-white h-2 rounded-full" style="width: 85%"></div>
                                </div>
                            </div>
                            <div class="absolute top-0 right-0 w-20 h-20 bg-white opacity-10 rounded-full transform translate-x-6 -translate-y-6"></div>
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <div class="px-6 mb-4">
                        <div class="grid grid-cols-4 gap-3">
                            <button class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <polygon points="23 7 16 12 23 17 23 7"/>
                                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                                <span class="text-xs font-medium text-text-primary">课程</span>
                            </button>
                            <button class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                                <span class="text-xs font-medium text-text-primary">作业</span>
                            </button>
                            <button class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                    <line x1="8" y1="21" x2="16" y2="21"/>
                                    <line x1="12" y1="17" x2="12" y2="21"/>
                                </svg>
                                <span class="text-xs font-medium text-text-primary">直播</span>
                            </button>
                            <button class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                                    <rect x="8" y="2" width="8" height="4" rx="1" ry="1"/>
                                </svg>
                                <span class="text-xs font-medium text-text-primary">证书</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 最近学习 -->
                    <div class="px-6 pb-20">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-text-primary">继续学习</h4>
                            <button class="text-sm text-blue-500 font-medium">查看全部</button>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white rounded-lg p-4 shadow-subtle">
                                <div class="flex items-start space-x-3">
                                    <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=60&h=45&fit=crop" 
                                         class="w-16 h-12 rounded-lg object-cover">
                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-medium text-text-primary text-sm mb-1">高等数学基础</h5>
                                        <p class="text-xs text-text-secondary mb-2">已学习 8/12 节</p>
                                        <div class="w-full bg-gray-200 rounded-full h-1">
                                            <div class="bg-blue-500 h-1 rounded-full" style="width: 67%"></div>
                                        </div>
                                    </div>
                                    <button class="bg-blue-500 text-white text-xs px-3 py-1 rounded-lg font-medium">继续</button>
                                </div>
                            </div>
                            <div class="bg-white rounded-lg p-4 shadow-subtle">
                                <div class="flex items-start space-x-3">
                                    <img src="https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=60&h=45&fit=crop" 
                                         class="w-16 h-12 rounded-lg object-cover">
                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-medium text-text-primary text-sm mb-1">英语口语训练</h5>
                                        <p class="text-xs text-text-secondary mb-2">已学习 5/15 节</p>
                                        <div class="w-full bg-gray-200 rounded-full h-1">
                                            <div class="bg-blue-500 h-1 rounded-full" style="width: 33%"></div>
                                        </div>
                                    </div>
                                    <button class="bg-blue-500 text-white text-xs px-3 py-1 rounded-lg font-medium">继续</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs font-medium text-blue-500">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <polygon points="23 7 16 12 23 17 23 7"/>
                                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                                <span class="text-xs text-text-light">课程</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                </svg>
                                <span class="text-xs text-text-light">学习</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 在线学习页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">在线学习页面</h3>
                <div class="screen bg-black rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-white">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-white rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-white rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 视频播放区域 -->
                    <div class="relative h-48 bg-black flex items-center justify-center">
                        <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=375&h=192&fit=crop" 
                             class="w-full h-full object-cover opacity-90">
                        <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                        
                        <!-- 播放控制 -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <button class="w-16 h-16 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white ml-1" viewBox="0 0 24 24" fill="currentColor">
                                    <polygon points="5,3 19,12 5,21"/>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- 顶部控制栏 -->
                        <div class="absolute top-4 left-6 right-6 flex items-center justify-between">
                            <button class="w-8 h-8 bg-black bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <polyline points="15,18 9,12 15,6"/>
                                </svg>
                            </button>
                            <div class="flex space-x-2">
                                <button class="w-8 h-8 bg-black bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                    </svg>
                                </button>
                                <button class="w-8 h-8 bg-black bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="18" cy="5" r="3"/>
                                        <circle cx="6" cy="12" r="3"/>
                                        <circle cx="18" cy="19" r="3"/>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 底部播放进度 -->
                        <div class="absolute bottom-4 left-6 right-6">
                            <div class="flex items-center space-x-3 text-white text-xs mb-2">
                                <span>15:32</span>
                                <div class="flex-1 bg-white bg-opacity-30 rounded-full h-1">
                                    <div class="bg-white h-1 rounded-full" style="width: 45%"></div>
                                </div>
                                <span>34:58</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 课程信息区域 -->
                    <div class="bg-white px-6 py-4">
                        <h4 class="font-bold text-text-primary mb-2">高等数学基础 - 第3章 导数与微分</h4>
                        <div class="flex items-center space-x-4 text-sm text-text-secondary mb-3">
                            <span>第3节/共12节</span>
                            <span>34分钟</span>
                            <span>已观看 15分钟</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                                     class="w-8 h-8 rounded-full object-cover">
                                <span class="text-sm font-medium text-text-primary">张教授</span>
                            </div>
                            <div class="flex space-x-2">
                                <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg font-medium">笔记</button>
                                <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg font-medium">提问</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 章节列表 -->
                    <div class="bg-white px-6 pb-20">
                        <div class="flex items-center justify-between mb-3">
                            <h5 class="font-medium text-text-primary">课程章节</h5>
                            <button class="text-sm text-blue-500">展开全部</button>
                        </div>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                                            <polygon points="5,3 19,12 5,21"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-text-primary">第3节：导数的定义</p>
                                        <p class="text-xs text-text-secondary">34分钟</p>
                                    </div>
                                </div>
                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-white" viewBox="0 0 24 24" fill="currentColor">
                                            <polygon points="5,3 19,12 5,21"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-text-primary">第4节：求导法则</p>
                                        <p class="text-xs text-text-secondary">28分钟</p>
                                    </div>
                                </div>
                                <svg class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                    <circle cx="12" cy="16" r="1"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部控制栏 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light p-4">
                        <div class="flex items-center justify-between">
                            <button class="flex items-center space-x-2 text-text-secondary">
                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <polygon points="19,20 9,12 19,4 19,20"/>
                                    <line x1="5" y1="19" x2="5" y2="5"/>
                                </svg>
                                <span class="text-sm">上一节</span>
                            </button>
                            <button class="bg-blue-500 text-white px-6 py-2 rounded-lg font-medium">
                                下一节
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付成功页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">支付成功页面</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 成功页面内容 -->
                    <div class="flex flex-col items-center justify-center h-full px-8">
                        <!-- 成功图标 -->
                        <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6">
                            <svg class="w-12 h-12 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        
                        <h2 class="text-2xl font-bold text-text-primary mb-2">支付成功</h2>
                        <p class="text-text-secondary text-center text-sm mb-8">恭喜您成功购买课程，现在可以开始学习了</p>
                        
                        <!-- 课程信息卡片 -->
                        <div class="w-full bg-white border border-border-light rounded-lg p-5 shadow-subtle mb-8">
                            <div class="flex items-start space-x-4">
                                <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=80&h=60&fit=crop" 
                                     class="w-20 h-15 rounded-lg object-cover">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-text-primary mb-2">高等数学基础课程</h3>
                                    <div class="space-y-1 text-sm">
                                        <div class="flex justify-between">
                                            <span class="text-text-secondary">课程时长</span>
                                            <span class="text-text-primary">12课时</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-secondary">有效期</span>
                                            <span class="text-text-primary">永久有效</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-secondary">支付金额</span>
                                            <span class="text-blue-500 font-semibold">¥299.00</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-text-secondary">订单号</span>
                                            <span class="text-text-primary">ED20241215001</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="w-full space-y-3">
                            <button class="w-full bg-blue-500 text-white font-semibold py-4 rounded-lg shadow-subtle">
                                立即开始学习
                            </button>
                            <button class="w-full bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg">
                                查看我的课程
                            </button>
                            <button class="w-full text-text-secondary font-medium py-2">
                                返回首页
                            </button>
                        </div>
                        
                        <!-- 温馨提示 -->
                        <div class="w-full bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-blue-500 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="12" y1="16" x2="12" y2="12"/>
                                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                                </svg>
                                <div>
                                    <h4 class="font-medium text-text-primary mb-1">学习提醒</h4>
                                    <p class="text-sm text-text-secondary">课程已添加到您的学习计划中，建议每天学习1-2节课，坚持完成课程学习。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行页面 -->
        <div class="grid grid-cols-3 gap-6 mb-8">
            
            <!-- 课程详情页 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">课程详情页</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 课程封面 -->
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=375&h=200&fit=crop" 
                             class="w-full h-48 object-cover">
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        <button class="absolute top-4 left-6 w-8 h-8 bg-white bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <div class="absolute top-4 right-6 flex space-x-2">
                            <button class="w-8 h-8 bg-white bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="18" cy="5" r="3"/>
                                    <circle cx="6" cy="12" r="3"/>
                                    <circle cx="18" cy="19" r="3"/>
                                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                </svg>
                            </button>
                            <button class="w-8 h-8 bg-white bg-opacity-20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                </svg>
                            </button>
                        </div>
                        <!-- 播放按钮 -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <button class="w-16 h-16 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-white ml-1" viewBox="0 0 24 24" fill="currentColor">
                                    <polygon points="5,3 19,12 5,21"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 课程信息 -->
                    <div class="px-6 py-4 bg-white">
                        <h2 class="text-lg font-bold text-text-primary mb-2">高等数学基础课程</h2>
                        <div class="flex items-center space-x-4 text-sm text-text-secondary mb-3">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                                    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                                </svg>
                                <span class="font-medium">4.8</span>
                            </div>
                            <span>1.2万人学习</span>
                            <span>12课时</span>
                        </div>
                        
                        <!-- 价格信息 -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-baseline space-x-2">
                                <span class="text-2xl font-bold text-blue-500">¥299</span>
                                <span class="text-sm text-text-light line-through">¥399</span>
                                <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-md font-medium">限时优惠</span>
                            </div>
                        </div>
                        
                        <!-- 讲师信息 -->
                        <div class="flex items-center space-x-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                                 class="w-10 h-10 rounded-full object-cover">
                            <div>
                                <h4 class="font-medium text-text-primary">张教授</h4>
                                <p class="text-xs text-text-secondary">北京大学数学系 · 20年教学经验</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 课程大纲 -->
                    <div class="px-6 pb-20">
                        <h4 class="font-semibold text-text-primary mb-3">课程大纲</h4>
                        <div class="space-y-2">
                            <div class="bg-white border border-border-light rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-text-primary text-sm">第1章：函数与极限</p>
                                        <p class="text-xs text-text-secondary">3节课 · 2小时15分</p>
                                    </div>
                                    <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="6,9 12,15 18,9"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-white border border-border-light rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-text-primary text-sm">第2章：一元函数微分学</p>
                                        <p class="text-xs text-text-secondary">4节课 · 3小时20分</p>
                                    </div>
                                    <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="6,9 12,15 18,9"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部购买栏 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light p-4">
                        <div class="flex items-center space-x-3">
                            <button class="w-12 h-12 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="9" cy="21" r="1"/>
                                    <circle cx="20" cy="21" r="1"/>
                                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                                </svg>
                            </button>
                            <button class="flex-1 bg-blue-500 text-white font-semibold py-3 rounded-lg shadow-subtle">
                                立即购买
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 教师工作台 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">教师工作台</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 教师信息头部 -->
                    <div class="px-6 py-4">
                        <div class="bg-white rounded-lg p-4 shadow-subtle">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face" 
                                         class="w-12 h-12 rounded-full object-cover border-2 border-blue-200">
                                    <div>
                                        <h3 class="font-semibold text-text-primary">张教授</h3>
                                        <p class="text-xs text-text-secondary">数学系高级讲师</p>
                                    </div>
                                </div>
                                <button class="w-8 h-8 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="3"/>
                                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据统计 -->
                    <div class="px-6 mb-4">
                        <div class="grid grid-cols-3 gap-3">
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="text-lg font-bold text-blue-500">5</div>
                                <div class="text-xs text-text-secondary">进行中课程</div>
                            </div>
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="text-lg font-bold text-blue-500">1,248</div>
                                <div class="text-xs text-text-secondary">总学员数</div>
                            </div>
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="text-lg font-bold text-blue-500">4.8</div>
                                <div class="text-xs text-text-secondary">平均评分</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 今日课程 -->
                    <div class="px-6 mb-4">
                        <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg p-4 text-white">
                            <h4 class="font-bold mb-2">今日直播课程</h4>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm opacity-90">高等数学基础</p>
                                    <p class="text-xs opacity-75">15:00-16:30 · 142人预约</p>
                                </div>
                                <button class="bg-white text-blue-500 font-medium text-sm px-4 py-2 rounded-lg">
                                    进入直播
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速操作 -->
                    <div class="px-6 pb-20">
                        <h4 class="font-semibold text-text-primary mb-3">快速操作</h4>
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <button class="bg-white border border-border-light rounded-lg p-4 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                <span class="text-sm font-medium text-text-primary">批改作业</span>
                                <div class="text-xs text-red-500 mt-1">12份待批改</div>
                            </button>
                            <button class="bg-white border border-border-light rounded-lg p-4 shadow-subtle text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-sm font-medium text-text-primary">学生提问</span>
                                <div class="text-xs text-red-500 mt-1">8个待回复</div>
                            </button>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="bg-white border border-border-light rounded-lg p-3 shadow-subtle">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-text-primary text-sm">课程数据统计</p>
                                        <p class="text-xs text-text-secondary">查看学习进度和完成情况</p>
                                    </div>
                                    <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="9,18 15,12 9,6"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="bg-white border border-border-light rounded-lg p-3 shadow-subtle">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-text-primary text-sm">创建新课程</p>
                                        <p class="text-xs text-text-secondary">发布视频课程或直播课</p>
                                    </div>
                                    <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="9,18 15,12 9,6"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 教师底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
                                    <line x1="8" y1="21" x2="16" y2="21"/>
                                    <line x1="12" y1="17" x2="12" y2="21"/>
                                </svg>
                                <span class="text-xs font-medium text-blue-500">工作台</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <polygon points="23 7 16 12 23 17 23 7"/>
                                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                                <span class="text-xs text-text-light">课程</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                                    <circle cx="9" cy="7" r="4"/>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                </svg>
                                <span class="text-xs text-text-light">学生</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <line x1="18" y1="20" x2="18" y2="10"/>
                                    <line x1="12" y1="20" x2="12" y2="4"/>
                                    <line x1="6" y1="20" x2="6" y2="14"/>
                                </svg>
                                <span class="text-xs text-text-light">统计</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 直播课堂页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">直播课堂</h3>
                <div class="screen bg-black rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-white">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-white rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-white rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 直播视频区域 -->
                    <div class="relative h-56 bg-black">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=375&h=224&fit=crop" 
                             class="w-full h-full object-cover opacity-90">
                        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                        
                        <!-- 直播状态 -->
                        <div class="absolute top-4 left-6">
                            <div class="bg-red-500 text-white text-xs px-2 py-1 rounded-md font-medium flex items-center space-x-1">
                                <div class="w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                <span>直播中</span>
                            </div>
                        </div>
                        
                        <!-- 观看人数 -->
                        <div class="absolute top-4 right-6">
                            <div class="bg-black bg-opacity-40 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-md">
                                142人观看
                            </div>
                        </div>
                        
                        <!-- 控制按钮 -->
                        <div class="absolute bottom-4 right-6 flex space-x-2">
                            <button class="w-8 h-8 bg-black bg-opacity-40 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M23 7l-10 5 10 5V7z"/>
                                    <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
                                </svg>
                            </button>
                            <button class="w-8 h-8 bg-black bg-opacity-40 backdrop-blur-sm rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="18" cy="5" r="3"/>
                                    <circle cx="6" cy="12" r="3"/>
                                    <circle cx="18" cy="19" r="3"/>
                                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- 课程信息覆盖层 -->
                        <div class="absolute bottom-4 left-6 right-20">
                            <h4 class="text-white font-semibold text-sm mb-1">高等数学基础 - 第3章实时讲解</h4>
                            <p class="text-white text-xs opacity-90">张教授 · 已播放 45分钟</p>
                        </div>
                    </div>
                    
                    <!-- 聊天互动区域 -->
                    <div class="bg-white px-4 py-3 h-80 flex flex-col">
                        <!-- 聊天头部 -->
                        <div class="flex items-center justify-between mb-3">
                            <h5 class="font-medium text-text-primary">课堂互动</h5>
                            <div class="flex space-x-2">
                                <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg">
                                    提问
                                </button>
                                <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg">
                                    笔记
                                </button>
                            </div>
                        </div>
                        
                        <!-- 聊天消息 -->
                        <div class="flex-1 space-y-3 overflow-y-auto">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <div class="flex items-start space-x-2">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=24&h=24&fit=crop&crop=face" 
                                         class="w-6 h-6 rounded-full object-cover">
                                    <div>
                                        <p class="text-xs text-blue-600 font-medium">张教授</p>
                                        <p class="text-sm text-text-primary">大家好，欢迎来到今天的直播课，我们开始第3章的学习</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-2">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=24&h=24&fit=crop&crop=face" 
                                     class="w-6 h-6 rounded-full object-cover">
                                <div>
                                    <p class="text-xs text-text-secondary">李同学</p>
                                    <p class="text-sm text-text-primary">老师好！</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-2">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=24&h=24&fit=crop&crop=face" 
                                     class="w-6 h-6 rounded-full object-cover">
                                <div>
                                    <p class="text-xs text-text-secondary">王同学</p>
                                    <p class="text-sm text-text-primary">请问导数的几何意义是什么？</p>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                                <div class="flex items-start space-x-2">
                                    <svg class="w-4 h-4 text-yellow-500 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                                        <circle cx="12" cy="12" r="10"/>
                                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                                    </svg>
                                    <div>
                                        <p class="text-xs text-yellow-600 font-medium">系统提醒</p>
                                        <p class="text-xs text-text-primary">有同学提问，老师请注意查看</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 输入框 -->
                        <div class="flex items-center space-x-2 pt-3 border-t border-border-light">
                            <div class="flex-1 bg-blue-50 border border-blue-200 rounded-lg px-3 py-2">
                                <span class="text-sm text-text-tertiary">输入聊天内容...</span>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <line x1="22" y1="2" x2="11" y2="13"/>
                                    <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第三行页面 -->
        <div class="grid grid-cols-3 gap-6 mb-8">
            
            <!-- 作业提交页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">作业提交</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 头部 -->
                    <div class="flex items-center justify-between px-6 py-4">
                        <button class="w-8 h-8 bg-white border border-border-light rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-text-secondary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <h1 class="font-bold text-text-primary">作业提交</h1>
                        <div class="w-8 h-8"></div>
                    </div>
                    
                    <!-- 作业信息 -->
                    <div class="px-6 pb-20 space-y-4">
                        <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                            <div class="flex items-start justify-between mb-3">
                                <div>
                                    <h4 class="font-semibold text-text-primary">第3章课后练习</h4>
                                    <p class="text-sm text-text-secondary">高等数学基础 · 张教授</p>
                                </div>
                                <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-md font-medium">待提交</span>
                            </div>
                            <div class="text-sm text-text-secondary space-y-1">
                                <p>截止时间：2024年12月18日 23:59</p>
                                <p>提交格式：PDF文档或图片</p>
                                <p>满分：100分</p>
                            </div>
                        </div>
                        
                        <!-- 作业题目 -->
                        <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                            <h5 class="font-medium text-text-primary mb-3">作业题目</h5>
                            <div class="space-y-3">
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <p class="text-sm font-medium text-text-primary mb-2">1. 计算下列函数的导数：</p>
                                    <div class="text-sm text-text-secondary space-y-1">
                                        <p>(1) f(x) = 3x² + 2x - 1</p>
                                        <p>(2) f(x) = sin(2x) + cos(x)</p>
                                        <p>(3) f(x) = ln(x) + e^x</p>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <p class="text-sm font-medium text-text-primary mb-2">2. 应用题：</p>
                                    <p class="text-sm text-text-secondary">某物体的位移函数为 s(t) = 2t³ - 3t² + t，求t=2时的瞬时速度。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 上传作业 -->
                        <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                            <h5 class="font-medium text-text-primary mb-3">上传作业</h5>
                            <div class="border-2 border-dashed border-blue-200 rounded-lg p-6 text-center mb-4">
                                <svg class="w-8 h-8 text-blue-400 mx-auto mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <p class="text-sm text-text-secondary mb-1">点击上传文件</p>
                                <p class="text-xs text-text-light">支持PDF、图片格式，不超过10MB</p>
                            </div>
                            
                            <!-- 已上传文件 -->
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <svg class="w-5 h-5 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                            <polyline points="14,2 14,8 20,8"/>
                                        </svg>
                                        <div>
                                            <p class="text-sm font-medium text-text-primary">作业答案.pdf</p>
                                            <p class="text-xs text-text-secondary">2.3MB</p>
                                        </div>
                                    </div>
                                    <button class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                        <svg class="w-3 h-3 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <line x1="18" y1="6" x2="6" y2="18"/>
                                            <line x1="6" y1="6" x2="18" y2="18"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 备注 -->
                        <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                            <h5 class="font-medium text-text-primary mb-3">作业说明（可选）</h5>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 h-20">
                                <span class="text-sm text-text-tertiary">请输入作业说明或遇到的问题...</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部提交按钮 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light p-4">
                        <div class="flex space-x-3">
                            <button class="flex-1 bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg">
                                保存草稿
                            </button>
                            <button class="flex-1 bg-blue-500 text-white font-medium py-3 rounded-lg shadow-subtle">
                                提交作业
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习统计页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">学习统计</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 头部 -->
                    <div class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h1 class="text-xl font-bold text-text-primary">学习报告</h1>
                            <button class="bg-blue-50 border border-blue-200 text-blue-500 text-sm px-3 py-1 rounded-lg font-medium">
                                本月
                            </button>
                        </div>
                        <p class="text-sm text-text-secondary mt-1">分析您的学习情况和进步</p>
                    </div>
                    
                    <!-- 总体数据 -->
                    <div class="px-6 mb-4">
                        <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg p-5 text-white">
                            <h4 class="font-bold mb-3">本月学习成果</h4>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold">32</div>
                                    <div class="text-xs opacity-90">学习天数</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold">48</div>
                                    <div class="text-xs opacity-90">学习时长(h)</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold">85%</div>
                                    <div class="text-xs opacity-90">完成率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 课程进度 -->
                    <div class="px-6 mb-4">
                        <h5 class="font-semibold text-text-primary mb-3">课程进度</h5>
                        <div class="space-y-3">
                            <div class="bg-white border border-border-light rounded-lg p-3 shadow-subtle">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-text-primary text-sm">高等数学基础</span>
                                    <span class="text-sm text-blue-500 font-medium">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>已学 9/12 节</span>
                                    <span>本周学习 3节</span>
                                </div>
                            </div>
                            
                            <div class="bg-white border border-border-light rounded-lg p-3 shadow-subtle">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-text-primary text-sm">英语口语训练</span>
                                    <span class="text-sm text-blue-500 font-medium">45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-text-secondary mt-1">
                                    <span>已学 7/15 节</span>
                                    <span>本周学习 2节</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 学习时间分布 -->
                    <div class="px-6 pb-20">
                        <h5 class="font-semibold text-text-primary mb-3">学习时间分布</h5>
                        <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-text-secondary">视频学习</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-text-primary">28.8h</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-text-secondary">练习作业</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-indigo-500 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-text-primary">14.4h</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-text-secondary">直播互动</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 10%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-text-primary">4.8h</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 成绩趋势 -->
                        <div class="mt-4">
                            <h5 class="font-semibold text-text-primary mb-3">成绩趋势</h5>
                            <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-sm text-text-secondary">平均分</span>
                                    <span class="text-lg font-bold text-blue-500">87.5</span>
                                </div>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">高等数学</span>
                                        <span class="font-medium text-text-primary">92分</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">英语口语</span>
                                        <span class="font-medium text-text-primary">83分</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 证书展示页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">证书展示</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 头部 -->
                    <div class="flex items-center justify-between px-6 py-4">
                        <button class="w-8 h-8 bg-white border border-border-light rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-text-secondary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <h1 class="font-bold text-text-primary">学习证书</h1>
                        <button class="w-8 h-8 bg-white border border-border-light rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-text-secondary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="18" cy="5" r="3"/>
                                <circle cx="6" cy="12" r="3"/>
                                <circle cx="18" cy="19" r="3"/>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- 证书展示 -->
                    <div class="px-6 pb-20 space-y-4">
                        <!-- 主要证书 -->
                        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 border-2 border-yellow-200 rounded-lg p-6 text-center relative overflow-hidden">
                            <div class="relative z-10">
                                <div class="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M14 9V5a3 3 0 0 0-6 0v4"/>
                                        <rect x="2" y="9" width="20" height="12" rx="2" ry="2"/>
                                        <circle cx="12" cy="15" r="1"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold text-yellow-800 mb-2">高等数学基础证书</h3>
                                <p class="text-sm text-yellow-700 mb-4">恭喜您完成全部课程学习</p>
                                <div class="space-y-1 text-sm text-yellow-700">
                                    <p>学员：李同学</p>
                                    <p>成绩：92分（优秀）</p>
                                    <p>颁发日期：2024年12月15日</p>
                                    <p>证书编号：CERT-2024-GM-001</p>
                                </div>
                                <div class="mt-4 space-x-2">
                                    <button class="bg-yellow-400 text-white font-medium text-sm px-4 py-2 rounded-lg">
                                        下载证书
                                    </button>
                                    <button class="bg-white border border-yellow-300 text-yellow-700 font-medium text-sm px-4 py-2 rounded-lg">
                                        分享证书
                                    </button>
                                </div>
                            </div>
                            <div class="absolute top-0 right-0 w-20 h-20 bg-yellow-300 opacity-20 rounded-full transform translate-x-6 -translate-y-6"></div>
                            <div class="absolute bottom-0 left-0 w-16 h-16 bg-yellow-300 opacity-20 rounded-full transform -translate-x-4 translate-y-4"></div>
                        </div>
                        
                        <!-- 其他证书列表 -->
                        <div class="space-y-3">
                            <h4 class="font-semibold text-text-primary">我的证书</h4>
                            
                            <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h5 class="font-medium text-text-primary">高等数学基础</h5>
                                        <p class="text-xs text-text-secondary">2024年12月15日获得 · 成绩：92分</p>
                                    </div>
                                    <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg">
                                        查看
                                    </button>
                                </div>
                            </div>
                            
                            <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <circle cx="12" cy="12" r="10"/>
                                            <path d="M12 6v6l4 2"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h5 class="font-medium text-text-primary">英语口语训练</h5>
                                        <p class="text-xs text-text-secondary">学习中 · 进度：45%</p>
                                    </div>
                                    <span class="bg-gray-100 text-text-secondary text-xs px-3 py-1 rounded-lg">
                                        未完成
                                    </span>
                                </div>
                            </div>
                            
                            <div class="bg-white border border-border-light rounded-lg p-4 shadow-subtle">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                                            <circle cx="12" cy="16" r="1"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h5 class="font-medium text-text-primary">线性代数进阶</h5>
                                        <p class="text-xs text-text-secondary">未开始</p>
                                    </div>
                                    <span class="bg-gray-100 text-text-secondary text-xs px-3 py-1 rounded-lg">
                                        未解锁
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 证书说明 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-blue-500 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="12" y1="16" x2="12" y2="12"/>
                                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                                </svg>
                                <div>
                                    <h4 class="font-medium text-text-primary mb-1">证书说明</h4>
                                    <p class="text-sm text-text-secondary">完成课程学习并通过考试后可获得电子证书。证书支持下载和分享，可作为学习成果的有效证明。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>