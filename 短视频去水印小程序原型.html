<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短视频去水印微信小程序原型 - 专业级设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'blue-25': '#F8FAFF',
                        'blue-50': '#F0F6FF',
                        'blue-75': '#E8F2FF',
                        'blue-100': '#E0EDFF',
                        'blue-200': '#C7DBFF',
                        'blue-300': '#A4C5FF',
                        'blue-400': '#6BA3FF',
                        'blue-500': '#4285FF',
                        'indigo-25': '#F8F9FF',
                        'indigo-50': '#F1F3FF',
                        'indigo-75': '#E9ECFF',
                        'indigo-100': '#E0E5FF',
                        'indigo-200': '#C9D1FF',
                        'indigo-300': '#ADBEFF',
                        'indigo-400': '#8FA7FF',
                        'indigo-500': '#7B92FF',
                        'warm-gray': '#FAFAFA',
                        'text-primary': '#1A1D23',
                        'text-secondary': '#4A5568',
                        'text-tertiary': '#718096',
                        'text-light': '#A0AEC0',
                        'border-light': '#F7FAFC',
                        'border-blue': '#C7DBFF',
                    },
                    fontFamily: {
                        'system': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
                    },
                    boxShadow: {
                        'subtle': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'soft': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif; }
        .screen { width: 375px; height: 667px; position: relative; overflow: hidden; }
        
        /* 内存存储变量 */
        :root {
            --current-url: '';
            --parsed-content: '';
            --content-type: 'video';
        }
    </style>
</head>
<body class="bg-warm-gray p-6">
    <div class="max-w-7xl mx-auto">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-text-primary mb-2">短视频去水印微信小程序原型</h1>
            <p class="text-text-secondary">专业级核心页面展示 - 企业级设计标准</p>
        </div>

        <!-- 核心页面展示 -->
        <div class="grid grid-cols-3 gap-6 mb-8">
            
            <!-- 首页 - 链接解析页 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">首页 - 链接解析</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>
                    
                    <!-- 头部区域 -->
                    <div class="px-6 py-4">
                        <div class="bg-white rounded-lg p-4 shadow-subtle">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                            <polyline points="7.5,4.21 12,6.81 16.5,4.21"/>
                                            <polyline points="7.5,19.79 7.5,14.6 3,12"/>
                                            <polyline points="21,12 16.5,14.6 16.5,19.79"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-text-primary">视频去水印</h3>
                                        <p class="text-xs text-text-secondary">一键解析，轻松下载</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="w-8 h-8 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </button>
                                    <button class="w-8 h-8 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <circle cx="12" cy="12" r="3"/>
                                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 链接输入区域 -->
                    <div class="px-6 mb-4">
                        <div class="bg-white rounded-lg p-5 shadow-subtle border border-blue-100">
                            <h4 class="font-semibold text-text-primary mb-3 flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                </svg>
                                粘贴链接
                            </h4>
                            <div class="relative mb-4">
                                <textarea 
                                    class="w-full h-20 p-3 border border-gray-200 rounded-lg text-sm resize-none focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500" 
                                    placeholder="请粘贴抖音、快手、小红书、微博等平台的分享链接..."
                                    id="urlInput"></textarea>
                                <button class="absolute bottom-2 right-2 bg-blue-500 text-white p-2 rounded-lg">
                                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                    </svg>
                                </button>
                            </div>
                            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold py-3 rounded-lg shadow-subtle">
                                一键解析
                            </button>
                        </div>
                    </div>
                    
                    <!-- 支持平台 -->
                    <div class="px-6 mb-4">
                        <h4 class="font-medium text-text-primary mb-3">支持平台</h4>
                        <div class="grid grid-cols-4 gap-3">
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="w-8 h-8 bg-black rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-text-primary">抖音</span>
                            </div>
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="w-8 h-8 bg-blue-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-text-primary">快手</span>
                            </div>
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="w-8 h-8 bg-red-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-text-primary">小红书</span>
                            </div>
                            <div class="bg-white rounded-lg p-3 shadow-subtle text-center">
                                <div class="w-8 h-8 bg-orange-500 rounded-lg mx-auto mb-2 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                    </svg>
                                </div>
                                <span class="text-xs font-medium text-text-primary">微博</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 使用说明 -->
                    <div class="px-6 pb-20">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-blue-500 mt-0.5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="10"/>
                                    <line x1="12" y1="16" x2="12" y2="12"/>
                                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                                </svg>
                                <div>
                                    <h4 class="font-medium text-text-primary mb-1">使用说明</h4>
                                    <p class="text-sm text-text-secondary">1. 复制短视频分享链接</p>
                                    <p class="text-sm text-text-secondary">2. 粘贴到输入框中</p>
                                    <p class="text-sm text-text-secondary">3. 点击解析即可获取无水印内容</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs font-medium text-blue-500">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs text-text-light">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频解析结果页 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">视频解析结果</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-6 py-3">
                        <button class="w-8 h-8 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-subtle">
                            <svg class="w-4 h-4 text-text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <h2 class="font-semibold text-text-primary">解析结果</h2>
                        <button class="w-8 h-8 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-subtle">
                            <svg class="w-4 h-4 text-text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="18" cy="5" r="3"/>
                                <circle cx="6" cy="12" r="3"/>
                                <circle cx="18" cy="19" r="3"/>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 视频预览区域 -->
                    <div class="px-6 mb-4">
                        <div class="bg-black rounded-lg overflow-hidden shadow-card">
                            <div class="relative aspect-video">
                                <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=343&h=193&fit=crop"
                                     class="w-full h-full object-cover">
                                <div class="absolute inset-0 bg-black bg-opacity-30"></div>

                                <!-- 播放按钮 -->
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <button class="w-16 h-16 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                        <svg class="w-8 h-8 text-white ml-1" viewBox="0 0 24 24" fill="currentColor">
                                            <polygon points="5,3 19,12 5,21"/>
                                        </svg>
                                    </button>
                                </div>

                                <!-- 时长标签 -->
                                <div class="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                                    00:15
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 视频信息 -->
                    <div class="px-6 mb-4">
                        <div class="bg-white rounded-lg p-4 shadow-subtle border border-blue-100">
                            <h3 class="font-semibold text-text-primary mb-2">今天的心情就像这个天气一样好☀️</h3>
                            <p class="text-sm text-text-secondary mb-3">分享一下今天拍摄的小视频，希望大家喜欢～记得点赞关注哦！</p>

                            <!-- 作者信息 -->
                            <div class="flex items-center space-x-3 mb-4">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face"
                                     class="w-10 h-10 rounded-full object-cover border-2 border-blue-200">
                                <div>
                                    <h4 class="font-medium text-text-primary">小美同学</h4>
                                    <p class="text-xs text-text-secondary">@xiaomei_daily</p>
                                </div>
                            </div>

                            <!-- 视频数据 -->
                            <div class="grid grid-cols-3 gap-4 text-center text-sm">
                                <div>
                                    <div class="font-semibold text-text-primary">1.2万</div>
                                    <div class="text-text-secondary text-xs">点赞</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-text-primary">3.5万</div>
                                    <div class="text-text-secondary text-xs">播放</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-text-primary">856</div>
                                    <div class="text-text-secondary text-xs">评论</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="px-6 pb-20">
                        <div class="space-y-3">
                            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold py-3 rounded-lg shadow-subtle flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <span>保存视频</span>
                            </button>

                            <div class="grid grid-cols-2 gap-3">
                                <button class="bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                    </svg>
                                    <span>复制链接</span>
                                </button>
                                <button class="bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="18" cy="5" r="3"/>
                                        <circle cx="6" cy="12" r="3"/>
                                        <circle cx="18" cy="19" r="3"/>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                    </svg>
                                    <span>分享</span>
                                </button>
                            </div>

                            <!-- 提示信息 -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-green-700">解析成功</span>
                                </div>
                                <p class="text-xs text-green-600 mt-1">已成功去除水印，可直接保存使用</p>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs text-text-light">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs text-text-light">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图文解析结果页 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">图文解析结果</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-6 py-3">
                        <button class="w-8 h-8 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-subtle">
                            <svg class="w-4 h-4 text-text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <h2 class="font-semibold text-text-primary">图文内容</h2>
                        <button class="w-8 h-8 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-subtle">
                            <svg class="w-4 h-4 text-text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="18" cy="5" r="3"/>
                                <circle cx="6" cy="12" r="3"/>
                                <circle cx="18" cy="19" r="3"/>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 图文内容区域 -->
                    <div class="px-6 mb-4">
                        <div class="bg-white rounded-lg p-4 shadow-subtle border border-blue-100">
                            <h3 class="font-semibold text-text-primary mb-3">春日穿搭分享｜简约风格搭配指南</h3>

                            <!-- 图片网格 -->
                            <div class="grid grid-cols-2 gap-2 mb-4">
                                <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=150&h=150&fit=crop"
                                     class="w-full h-32 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=150&h=150&fit=crop"
                                     class="w-full h-32 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1445205170230-053b83016050?w=150&h=150&fit=crop"
                                     class="w-full h-32 object-cover rounded-lg">
                                <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=150&h=150&fit=crop"
                                     class="w-full h-32 object-cover rounded-lg">
                            </div>

                            <p class="text-sm text-text-secondary mb-3">
                                今天分享几套简约风格的春日穿搭，都是比较百搭的款式，适合日常通勤和休闲场合。主要以基础色为主，搭配一些小配饰来增加亮点。
                            </p>

                            <!-- 作者信息 -->
                            <div class="flex items-center space-x-3 mb-4">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face"
                                     class="w-10 h-10 rounded-full object-cover border-2 border-blue-200">
                                <div>
                                    <h4 class="font-medium text-text-primary">时尚小达人</h4>
                                    <p class="text-xs text-text-secondary">@fashion_daily</p>
                                </div>
                            </div>

                            <!-- 互动数据 -->
                            <div class="grid grid-cols-3 gap-4 text-center text-sm">
                                <div>
                                    <div class="font-semibold text-text-primary">2.8万</div>
                                    <div class="text-text-secondary text-xs">点赞</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-text-primary">5.2万</div>
                                    <div class="text-text-secondary text-xs">浏览</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-text-primary">1.2千</div>
                                    <div class="text-text-secondary text-xs">收藏</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="px-6 pb-20">
                        <div class="space-y-3">
                            <button class="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white font-semibold py-3 rounded-lg shadow-subtle flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <span>保存全部图片</span>
                            </button>

                            <div class="grid grid-cols-2 gap-3">
                                <button class="bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                                    </svg>
                                    <span>逐张保存</span>
                                </button>
                                <button class="bg-white border border-blue-200 text-blue-500 font-medium py-3 rounded-lg flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="18" cy="5" r="3"/>
                                        <circle cx="6" cy="12" r="3"/>
                                        <circle cx="18" cy="19" r="3"/>
                                        <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                        <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                    </svg>
                                    <span>分享</span>
                                </button>
                            </div>

                            <!-- 提示信息 -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span class="text-sm font-medium text-green-700">解析成功</span>
                                </div>
                                <p class="text-xs text-green-600 mt-1">共解析到4张高清图片，已去除水印</p>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs text-text-light">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs text-text-light">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">应用设置</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-6 py-3">
                        <button class="w-8 h-8 bg-white border border-gray-200 rounded-lg flex items-center justify-center shadow-subtle">
                            <svg class="w-4 h-4 text-text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <polyline points="15,18 9,12 15,6"/>
                            </svg>
                        </button>
                        <h2 class="font-semibold text-text-primary">设置</h2>
                        <div class="w-8 h-8"></div>
                    </div>

                    <!-- 设置选项 -->
                    <div class="px-6 pb-20">
                        <div class="space-y-4">
                            <!-- 下载设置 -->
                            <div class="bg-white rounded-lg shadow-subtle">
                                <div class="p-4 border-b border-gray-100">
                                    <h4 class="font-medium text-text-primary">下载设置</h4>
                                </div>
                                <div class="space-y-0">
                                    <div class="flex items-center justify-between p-4">
                                        <div>
                                            <p class="font-medium text-text-primary">自动保存到相册</p>
                                            <p class="text-sm text-text-secondary">解析完成后自动保存</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 shadow-sm"></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-4">
                                        <div>
                                            <p class="font-medium text-text-primary">高清画质优先</p>
                                            <p class="text-sm text-text-secondary">优先下载高清版本</p>
                                        </div>
                                        <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 shadow-sm"></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-4">
                                        <div>
                                            <p class="font-medium text-text-primary">WiFi环境下载</p>
                                            <p class="text-sm text-text-secondary">仅在WiFi环境下自动下载</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 shadow-sm"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 通知设置 -->
                            <div class="bg-white rounded-lg shadow-subtle">
                                <div class="p-4 border-b border-gray-100">
                                    <h4 class="font-medium text-text-primary">通知设置</h4>
                                </div>
                                <div class="space-y-0">
                                    <div class="flex items-center justify-between p-4">
                                        <div>
                                            <p class="font-medium text-text-primary">解析完成通知</p>
                                            <p class="text-sm text-text-secondary">解析成功后发送通知</p>
                                        </div>
                                        <div class="w-12 h-6 bg-blue-500 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 shadow-sm"></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-4">
                                        <div>
                                            <p class="font-medium text-text-primary">功能更新提醒</p>
                                            <p class="text-sm text-text-secondary">新功能上线时提醒</p>
                                        </div>
                                        <div class="w-12 h-6 bg-gray-300 rounded-full relative">
                                            <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 shadow-sm"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 其他设置 -->
                            <div class="bg-white rounded-lg shadow-subtle">
                                <div class="p-4 border-b border-gray-100">
                                    <h4 class="font-medium text-text-primary">其他</h4>
                                </div>
                                <div class="space-y-0">
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div>
                                            <p class="font-medium text-text-primary text-left">清除缓存</p>
                                            <p class="text-sm text-text-secondary text-left">清除应用缓存数据</p>
                                        </div>
                                        <div class="text-sm text-text-light">128MB</div>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div>
                                            <p class="font-medium text-text-primary text-left">检查更新</p>
                                            <p class="text-sm text-text-secondary text-left">检查应用更新</p>
                                        </div>
                                        <div class="text-sm text-green-500">已是最新</div>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div>
                                            <p class="font-medium text-text-primary text-left">用户协议</p>
                                            <p class="text-sm text-text-secondary text-left">查看用户使用协议</p>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div>
                                            <p class="font-medium text-text-primary text-left">隐私政策</p>
                                            <p class="text-sm text-text-secondary text-left">查看隐私保护政策</p>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs text-text-light">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs text-text-light">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简单的交互脚本 -->
        <script>
            // 内存存储对象
            const appData = {
                currentUrl: '',
                parsedContent: null,
                contentType: 'video', // 'video' 或 'images'
                history: []
            };

            // 模拟解析功能
            function parseUrl(url) {
                // 简单的URL类型判断
                if (url.includes('douyin') || url.includes('tiktok') || url.includes('kuaishou')) {
                    appData.contentType = 'video';
                } else if (url.includes('xiaohongshu') || url.includes('weibo')) {
                    appData.contentType = Math.random() > 0.5 ? 'video' : 'images';
                }

                appData.currentUrl = url;
                appData.parsedContent = {
                    title: '解析成功的内容标题',
                    author: '内容作者',
                    platform: '平台名称'
                };

                // 添加到历史记录
                appData.history.unshift({
                    url: url,
                    type: appData.contentType,
                    title: appData.parsedContent.title,
                    author: appData.parsedContent.author,
                    platform: appData.parsedContent.platform,
                    timestamp: new Date()
                });

                return appData.parsedContent;
            }

            // 模拟保存功能
            function saveContent() {
                console.log('保存内容:', appData.parsedContent);
                alert('保存成功！');
            }

            // 模拟复制链接功能
            function copyLink() {
                console.log('复制链接:', appData.currentUrl);
                alert('链接已复制到剪贴板！');
            }

            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', function() {
                console.log('短视频去水印小程序原型已加载');
                console.log('支持的功能：链接解析、视频下载、图文保存、历史记录');
            });
        </script>
    </div>
</body>
</html>

        <!-- 第二行页面 -->
        <div class="grid grid-cols-3 gap-6 mb-8">

            <!-- 历史记录页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">历史记录</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>

                    <!-- 顶部导航 -->
                    <div class="flex items-center justify-between px-6 py-3">
                        <h2 class="font-semibold text-text-primary">解析历史</h2>
                        <button class="text-sm text-blue-500 font-medium">清空</button>
                    </div>

                    <!-- 筛选标签 -->
                    <div class="px-6 mb-4">
                        <div class="flex space-x-2">
                            <button class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg font-medium">全部</button>
                            <button class="bg-white border border-gray-200 text-text-secondary text-sm px-4 py-2 rounded-lg">视频</button>
                            <button class="bg-white border border-gray-200 text-text-secondary text-sm px-4 py-2 rounded-lg">图文</button>
                        </div>
                    </div>

                    <!-- 历史记录列表 -->
                    <div class="px-6 pb-20">
                        <div class="space-y-3">
                            <!-- 视频记录 -->
                            <div class="bg-white rounded-lg p-4 shadow-subtle border border-gray-100">
                                <div class="flex items-start space-x-3">
                                    <div class="relative">
                                        <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=60&h=45&fit=crop"
                                             class="w-16 h-12 rounded-lg object-cover">
                                        <div class="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                                                <polygon points="5,3 19,12 5,21"/>
                                            </svg>
                                        </div>
                                        <div class="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white text-xs px-1 rounded">
                                            00:15
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-medium text-text-primary text-sm mb-1 truncate">今天的心情就像这个天气一样好☀️</h4>
                                        <p class="text-xs text-text-secondary mb-2">@小美同学 · 抖音</p>
                                        <p class="text-xs text-text-light">2小时前</p>
                                    </div>
                                    <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg font-medium">
                                        重新下载
                                    </button>
                                </div>
                            </div>

                            <!-- 图文记录 -->
                            <div class="bg-white rounded-lg p-4 shadow-subtle border border-gray-100">
                                <div class="flex items-start space-x-3">
                                    <div class="relative">
                                        <div class="w-16 h-12 rounded-lg overflow-hidden bg-gray-100">
                                            <div class="grid grid-cols-2 gap-0.5 h-full">
                                                <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=30&h=24&fit=crop"
                                                     class="w-full h-full object-cover">
                                                <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=30&h=24&fit=crop"
                                                     class="w-full h-full object-cover">
                                                <img src="https://images.unsplash.com/photo-1445205170230-053b83016050?w=30&h=24&fit=crop"
                                                     class="w-full h-full object-cover">
                                                <div class="bg-black bg-opacity-50 flex items-center justify-center">
                                                    <span class="text-white text-xs font-medium">+2</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-medium text-text-primary text-sm mb-1 truncate">春日穿搭分享｜简约风格搭配指南</h4>
                                        <p class="text-xs text-text-secondary mb-2">@时尚小达人 · 小红书</p>
                                        <p class="text-xs text-text-light">1天前</p>
                                    </div>
                                    <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg font-medium">
                                        重新下载
                                    </button>
                                </div>
                            </div>

                            <!-- 更多记录 -->
                            <div class="bg-white rounded-lg p-4 shadow-subtle border border-gray-100">
                                <div class="flex items-start space-x-3">
                                    <div class="relative">
                                        <img src="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=60&h=45&fit=crop"
                                             class="w-16 h-12 rounded-lg object-cover">
                                        <div class="absolute inset-0 bg-black bg-opacity-30 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" viewBox="0 0 24 24" fill="currentColor">
                                                <polygon points="5,3 19,12 5,21"/>
                                            </svg>
                                        </div>
                                        <div class="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white text-xs px-1 rounded">
                                            01:23
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-medium text-text-primary text-sm mb-1 truncate">美食制作教程分享</h4>
                                        <p class="text-xs text-text-secondary mb-2">@美食达人 · 快手</p>
                                        <p class="text-xs text-text-light">3天前</p>
                                    </div>
                                    <button class="bg-blue-50 border border-blue-200 text-blue-500 text-xs px-3 py-1 rounded-lg font-medium">
                                        重新下载
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs text-text-light">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs font-medium text-blue-500">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs text-text-light">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人中心页面 -->
            <div class="bg-white rounded-lg shadow-card p-4">
                <h3 class="text-sm font-semibold text-text-primary mb-3 text-center">个人中心</h3>
                <div class="screen bg-blue-25 rounded-lg">
                    <!-- 状态栏 -->
                    <div class="flex justify-between items-center px-6 pt-3 text-xs text-text-primary">
                        <span class="font-medium">9:41</span>
                        <div class="flex items-center space-x-1">
                            <div class="w-4 h-2 bg-text-primary rounded-sm opacity-80"></div>
                            <div class="w-6 h-3 border border-text-primary rounded-sm opacity-80"></div>
                        </div>
                    </div>

                    <!-- 用户信息头部 -->
                    <div class="px-6 py-4">
                        <div class="bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg p-5 text-white relative overflow-hidden">
                            <div class="relative z-10">
                                <div class="flex items-center space-x-4 mb-4">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face"
                                         class="w-15 h-15 rounded-full object-cover border-3 border-white">
                                    <div>
                                        <h3 class="font-bold text-lg">用户昵称</h3>
                                        <p class="text-sm opacity-90">微信用户</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 text-center">
                                    <div>
                                        <div class="text-xl font-bold">156</div>
                                        <div class="text-xs opacity-90">解析次数</div>
                                    </div>
                                    <div>
                                        <div class="text-xl font-bold">89</div>
                                        <div class="text-xs opacity-90">保存文件</div>
                                    </div>
                                    <div>
                                        <div class="text-xl font-bold">12</div>
                                        <div class="text-xs opacity-90">使用天数</div>
                                    </div>
                                </div>
                            </div>
                            <div class="absolute top-0 right-0 w-20 h-20 bg-white opacity-10 rounded-full transform translate-x-6 -translate-y-6"></div>
                        </div>
                    </div>

                    <!-- 功能菜单 -->
                    <div class="px-6 pb-20">
                        <div class="space-y-4">
                            <!-- 常用功能 -->
                            <div class="bg-white rounded-lg shadow-subtle">
                                <div class="p-4 border-b border-gray-100">
                                    <h4 class="font-medium text-text-primary">常用功能</h4>
                                </div>
                                <div class="space-y-0">
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-blue-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">解析历史</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-green-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                                    <polyline points="7,10 12,15 17,10"/>
                                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">下载管理</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-purple-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">我的收藏</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- 设置选项 -->
                            <div class="bg-white rounded-lg shadow-subtle">
                                <div class="p-4 border-b border-gray-100">
                                    <h4 class="font-medium text-text-primary">设置</h4>
                                </div>
                                <div class="space-y-0">
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-orange-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <circle cx="12" cy="12" r="3"/>
                                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">应用设置</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <line x1="12" y1="16" x2="12" y2="12"/>
                                                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">帮助与反馈</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                    <button class="w-full flex items-center justify-between p-4 hover:bg-gray-50">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-gray-500" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <line x1="12" y1="16" x2="12" y2="12"/>
                                                    <line x1="12" y1="8" x2="12.01" y2="8"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium text-text-primary">关于我们</span>
                                        </div>
                                        <svg class="w-4 h-4 text-text-light" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <polyline points="9,18 15,12 9,6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 底部导航 -->
                    <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-border-light">
                        <div class="flex justify-around py-3">
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                </svg>
                                <span class="text-xs text-text-light">首页</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-text-light mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                                <span class="text-xs text-text-light">历史</span>
                            </div>
                            <div class="text-center">
                                <svg class="w-6 h-6 text-blue-500 mx-auto mb-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                                </svg>
                                <span class="text-xs font-medium text-blue-500">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
